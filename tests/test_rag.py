"""
Deep Risk RAG 系统测试用例
"""

import os
import sys
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.document_loader import DocumentProcessor, load_documents
from src.embeddings import GTELargeEmbeddings
from src.vector_store import ChromaVectorStore, VectorStoreManager
from src.rag_chain import DeepSeekRAGChain
from langchain_core.documents import Document


class TestDocumentProcessor:
    """文档处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.processor = DocumentProcessor(chunk_size=100, chunk_overlap=20)
    
    def test_get_file_type(self):
        """测试文件类型识别"""
        assert self.processor.get_file_type("test.pdf") == "pdf"
        assert self.processor.get_file_type("test.txt") == "text"
        assert self.processor.get_file_type("test.docx") == "word"
        assert self.processor.get_file_type("test.unknown") is None
    
    def test_preprocess_text(self):
        """测试文本预处理"""
        text = "  这是一个   测试文本  \n\n  包含多余空格  "
        processed = self.processor.preprocess_text(text)
        assert processed == "这是一个 测试文本 包含多余空格"
    
    def test_split_documents(self):
        """测试文档分割"""
        # 创建测试文档
        long_text = "这是一个很长的文本。" * 20
        doc = Document(page_content=long_text, metadata={"source": "test.txt"})
        
        # 分割文档
        split_docs = self.processor.split_documents([doc])
        
        # 验证结果
        assert len(split_docs) > 1
        for chunk in split_docs:
            assert len(chunk.page_content) <= self.processor.chunk_size + 50  # 允许一些误差
            assert "chunk_id" in chunk.metadata
            assert "source" in chunk.metadata


class TestEmbeddings:
    """嵌入服务测试"""
    
    @pytest.fixture
    def mock_embeddings(self):
        """模拟嵌入服务"""
        with patch('src.embeddings.SentenceTransformer') as mock_st:
            # 模拟模型
            mock_model = Mock()
            mock_model.encode.return_value = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
            mock_model.get_sentence_embedding_dimension.return_value = 3
            mock_st.return_value = mock_model
            
            # 创建嵌入服务
            embeddings = GTELargeEmbeddings(
                cache_dir=tempfile.mkdtemp(),
                enable_cache=False
            )
            return embeddings
    
    def test_embed_documents(self, mock_embeddings):
        """测试文档嵌入"""
        texts = ["测试文本1", "测试文本2"]
        embeddings = mock_embeddings.embed_documents(texts)
        
        assert len(embeddings) == 2
        assert len(embeddings[0]) == 3  # 嵌入维度
        assert isinstance(embeddings[0], list)
    
    def test_embed_query(self, mock_embeddings):
        """测试查询嵌入"""
        query = "测试查询"
        embedding = mock_embeddings.embed_query(query)
        
        assert len(embedding) == 3
        assert isinstance(embedding, list)
    
    def test_similarity(self, mock_embeddings):
        """测试相似度计算"""
        emb1 = [1.0, 0.0, 0.0]
        emb2 = [1.0, 0.0, 0.0]
        emb3 = [0.0, 1.0, 0.0]
        
        # 相同向量相似度应该为1
        sim1 = mock_embeddings.similarity(emb1, emb2)
        assert abs(sim1 - 1.0) < 0.001
        
        # 正交向量相似度应该为0
        sim2 = mock_embeddings.similarity(emb1, emb3)
        assert abs(sim2 - 0.0) < 0.001


class TestVectorStore:
    """向量存储测试"""
    
    @pytest.fixture
    def mock_vector_store(self):
        """模拟向量存储"""
        with patch('src.embeddings.SentenceTransformer'):
            with tempfile.TemporaryDirectory() as temp_dir:
                embeddings = GTELargeEmbeddings(
                    cache_dir=temp_dir,
                    enable_cache=False
                )
                
                vector_store = ChromaVectorStore(
                    embeddings=embeddings,
                    persist_directory=temp_dir,
                    collection_name="test_collection"
                )
                return vector_store
    
    def test_add_documents(self, mock_vector_store):
        """测试添加文档"""
        docs = [
            Document(page_content="测试文档1", metadata={"source": "test1.txt"}),
            Document(page_content="测试文档2", metadata={"source": "test2.txt"})
        ]
        
        # 模拟向量存储的add_documents方法
        with patch.object(mock_vector_store.vector_store, 'add_documents') as mock_add:
            mock_add.return_value = ["id1", "id2"]
            
            doc_ids = mock_vector_store.add_documents(docs)
            
            assert len(doc_ids) == 2
            mock_add.assert_called_once()
    
    def test_similarity_search(self, mock_vector_store):
        """测试相似度搜索"""
        # 模拟搜索结果
        mock_results = [
            Document(page_content="相关文档1", metadata={"source": "doc1.txt"}),
            Document(page_content="相关文档2", metadata={"source": "doc2.txt"})
        ]
        
        with patch.object(mock_vector_store.vector_store, 'similarity_search') as mock_search:
            mock_search.return_value = mock_results
            
            results = mock_vector_store.similarity_search("测试查询", k=2)
            
            assert len(results) == 2
            assert results[0].page_content == "相关文档1"
            mock_search.assert_called_once_with(query="测试查询", k=2, filter=None)


class TestRAGChain:
    """RAG 链测试"""
    
    @pytest.fixture
    def mock_rag_chain(self):
        """模拟 RAG 链"""
        with patch('src.embeddings.SentenceTransformer'):
            with patch('langchain_deepseek.ChatDeepSeek') as mock_llm:
                with tempfile.TemporaryDirectory() as temp_dir:
                    # 模拟 LLM
                    mock_llm_instance = Mock()
                    mock_llm_instance.invoke.return_value = Mock(content="这是测试回答")
                    mock_llm.return_value = mock_llm_instance
                    
                    # 创建嵌入服务
                    embeddings = GTELargeEmbeddings(
                        cache_dir=temp_dir,
                        enable_cache=False
                    )
                    
                    # 创建向量存储
                    vector_store = ChromaVectorStore(
                        embeddings=embeddings,
                        persist_directory=temp_dir,
                        collection_name="test_collection"
                    )
                    
                    # 创建 RAG 链
                    rag_chain = DeepSeekRAGChain(
                        vector_store=vector_store,
                        api_key="test_key"
                    )
                    
                    return rag_chain
    
    def test_ask_question(self, mock_rag_chain):
        """测试问答功能"""
        # 模拟检索结果
        mock_docs = [
            Document(page_content="相关内容1", metadata={"source": "doc1.txt", "file_name": "doc1.txt"}),
            Document(page_content="相关内容2", metadata={"source": "doc2.txt", "file_name": "doc2.txt"})
        ]
        
        with patch.object(mock_rag_chain.retriever, 'invoke') as mock_retrieve:
            mock_retrieve.return_value = mock_docs
            
            with patch.object(mock_rag_chain.rag_chain, 'invoke') as mock_chain:
                mock_chain.return_value = "这是基于文档的回答"
                
                result = mock_rag_chain.ask("测试问题")
                
                assert result["success"] is True
                assert "这是基于文档的回答" in result["answer"]
                assert len(result["sources"]) == 2
                assert result["retrieved_docs_count"] == 2
    
    def test_ask_empty_question(self, mock_rag_chain):
        """测试空问题"""
        result = mock_rag_chain.ask("")
        
        assert result["success"] is False
        assert "请提供一个有效的问题" in result["answer"]
    
    def test_ask_no_documents(self, mock_rag_chain):
        """测试没有相关文档的情况"""
        with patch.object(mock_rag_chain.retriever, 'invoke') as mock_retrieve:
            mock_retrieve.return_value = []
            
            result = mock_rag_chain.ask("测试问题")
            
            assert result["success"] is True
            assert "没有找到与您问题相关的信息" in result["answer"]
            assert result["retrieved_docs_count"] == 0


class TestIntegration:
    """集成测试"""
    
    def test_document_to_vector_store_pipeline(self):
        """测试从文档到向量存储的完整流程"""
        with patch('src.embeddings.SentenceTransformer'):
            with tempfile.TemporaryDirectory() as temp_dir:
                # 创建测试文档
                test_file = Path(temp_dir) / "test.txt"
                test_file.write_text("这是一个测试文档，包含一些测试内容。")
                
                # 创建文档处理器
                processor = DocumentProcessor(chunk_size=50, chunk_overlap=10)
                
                # 加载文档
                documents = processor.load_single_file(test_file)
                assert len(documents) == 1
                
                # 分割文档
                split_docs = processor.split_documents(documents)
                assert len(split_docs) >= 1
                
                # 创建向量存储
                embeddings = GTELargeEmbeddings(
                    cache_dir=temp_dir,
                    enable_cache=False
                )
                
                vector_store = ChromaVectorStore(
                    embeddings=embeddings,
                    persist_directory=temp_dir,
                    collection_name="test_integration"
                )
                
                # 模拟添加文档
                with patch.object(vector_store.vector_store, 'add_documents') as mock_add:
                    mock_add.return_value = ["doc_id_1"]
                    
                    doc_ids = vector_store.add_documents(split_docs)
                    assert len(doc_ids) == len(split_docs)


# 运行测试的便捷函数
def run_tests():
    """运行所有测试"""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
