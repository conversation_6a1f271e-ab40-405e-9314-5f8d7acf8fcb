# Deep Risk RAG 系统

基于 LangChain + DeepSeek + gte-large 的本地知识库 RAG（检索增强生成）系统。

## 功能特性

- 🤖 **智能问答**: 使用 DeepSeek API 提供高质量的对话体验
- 📚 **本地知识库**: 支持多种文档格式（PDF、DOCX、TXT、MD 等）
- 🔍 **语义检索**: 使用 gte-large 嵌入模型进行精确的语义搜索
- 💾 **向量存储**: 基于 Chroma 的高效向量数据库
- 🚀 **本地部署**: 嵌入模型完全本地运行，保护数据隐私
- ⚡ **高性能**: 优化的文档处理和检索流程

## 技术架构

- **LLM**: DeepSeek API (deepseek-chat)
- **嵌入模型**: gte-large (本地运行)
- **向量数据库**: Chroma
- **文档处理**: LangChain Document Loaders
- **框架**: LangChain + HuggingFace Transformers

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd deep-risk-rag

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加你的 DeepSeek API Key
# DEEPSEEK_API_KEY=your_api_key_here
```

### 3. 下载模型

```bash
# 下载 gte-large 嵌入模型到本地
python models/download_models.py
```

### 4. 添加文档

将你的文档文件放入 `data/documents/` 目录中。

### 5. 运行系统

```bash
# 启动交互式问答
python main.py
```

## 项目结构

```
deep-risk-rag/
├── requirements.txt          # 依赖包列表
├── config.py                # 系统配置
├── main.py                  # 主程序入口
├── .env.example             # 环境变量模板
├── models/
│   └── download_models.py   # 模型下载脚本
├── src/                     # 核心源码
│   ├── __init__.py
│   ├── document_loader.py   # 文档加载器
│   ├── embeddings.py        # 嵌入服务
│   ├── vector_store.py      # 向量数据库
│   ├── retriever.py         # 检索器
│   └── rag_chain.py         # RAG 链
├── data/
│   └── documents/           # 文档存储目录
├── cache/
│   ├── models/              # 模型缓存
│   └── chroma_db/           # 向量数据库
└── tests/                   # 测试文件
    └── test_rag.py
```

## 使用说明

### 支持的文档格式

- PDF (.pdf)
- Word 文档 (.docx)
- 文本文件 (.txt)
- Markdown (.md)
- Excel (.xlsx)
- PowerPoint (.pptx)

### 配置选项

主要配置项在 `config.py` 中，包括：

- `CHUNK_SIZE`: 文档分块大小（默认 1000）
- `RETRIEVAL_TOP_K`: 检索返回的文档数量（默认 5）
- `TEMPERATURE`: LLM 生成温度（默认 0.1）
- `SIMILARITY_THRESHOLD`: 相似度阈值（默认 0.7）

## 开发指南

### 运行测试

```bash
pytest tests/
```

### 代码格式化

```bash
black src/ tests/
flake8 src/ tests/
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
