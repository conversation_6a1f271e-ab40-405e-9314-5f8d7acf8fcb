"""
风险评估引擎
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime
from dataclasses import dataclass

from .risk_config import RiskAnalysisConfig, RiskLevel, PersonalCreditIndicator, RiskThreshold

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class RiskFactor:
    """风险因子"""
    name: str
    value: float
    weight: float
    risk_level: RiskLevel
    description: str


@dataclass
class RiskAssessmentResult:
    """风险评估结果"""
    overall_risk_score: float
    risk_level: RiskLevel
    default_probability: float
    risk_factors: List[RiskFactor]
    warning_signals: List[str]
    recommendations: List[str]
    confidence_score: float


class RiskAssessmentEngine:
    """风险评估引擎"""
    
    def __init__(self):
        """初始化风险评估引擎"""
        self.risk_config = RiskAnalysisConfig()
        
        # 个人信贷风险评估模型参数
        self.model_weights = {
            "income_risk": 0.30,         # 收入风险
            "debt_risk": 0.25,           # 负债风险
            "credit_history_risk": 0.20, # 信用历史风险
            "stability_risk": 0.15,      # 稳定性风险
            "demographic_risk": 0.10     # 人口统计学风险
        }
        
        logger.info("风险评估引擎初始化完成")
    
    def assess_income_risk(self, metrics: Dict[str, float]) -> RiskFactor:
        """
        评估收入风险

        Args:
            metrics: 个人信贷指标字典

        Returns:
            收入风险因子
        """
        # 获取收入相关指标
        monthly_income = metrics.get("月收入", 5000)
        income_stability = metrics.get("收入稳定性", 0.5)
        employment_years = metrics.get("工作年限", 2)

        # 计算收入风险评分
        risk_score = 0.0

        # 月收入评估
        if monthly_income < 3000:
            risk_score += 0.4
        elif monthly_income < 5000:
            risk_score += 0.25
        elif monthly_income < 8000:
            risk_score += 0.1

        # 收入稳定性评估
        if income_stability < 0.4:
            risk_score += 0.3
        elif income_stability < 0.6:
            risk_score += 0.2
        elif income_stability < 0.8:
            risk_score += 0.1

        # 工作年限评估
        if employment_years < 1:
            risk_score += 0.2
        elif employment_years < 2:
            risk_score += 0.1

        # 确定风险等级
        if risk_score >= 0.6:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.4:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        description = f"月收入: {monthly_income:,.0f}, 工作年限: {employment_years}年, 稳定性: {income_stability:.1f}"

        return RiskFactor(
            name="收入风险",
            value=risk_score,
            weight=self.model_weights["income_risk"],
            risk_level=risk_level,
            description=description
        )
    
    def assess_debt_risk(self, metrics: Dict[str, float]) -> RiskFactor:
        """
        评估负债风险

        Args:
            metrics: 个人信贷指标字典

        Returns:
            负债风险因子
        """
        # 获取负债相关指标
        debt_to_income = metrics.get("负债收入比", 0.3)
        existing_debt = metrics.get("现有负债", 0)
        loan_amount = metrics.get("贷款金额", 100000)
        monthly_income = metrics.get("月收入", 5000)

        # 计算负债风险评分
        risk_score = 0.0

        # 负债收入比评估
        if debt_to_income >= 0.7:
            risk_score += 0.5
        elif debt_to_income >= 0.5:
            risk_score += 0.3
        elif debt_to_income >= 0.3:
            risk_score += 0.15

        # 贷款收入比评估
        if monthly_income > 0:
            loan_to_monthly_income = loan_amount / monthly_income
            if loan_to_monthly_income >= 50:  # 贷款金额超过50倍月收入
                risk_score += 0.3
            elif loan_to_monthly_income >= 30:
                risk_score += 0.2
            elif loan_to_monthly_income >= 20:
                risk_score += 0.1

        # 确定风险等级
        if risk_score >= 0.6:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.4:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        description = f"负债收入比: {debt_to_income:.2%}, 现有负债: {existing_debt:,.0f}, 申请金额: {loan_amount:,.0f}"

        return RiskFactor(
            name="负债风险",
            value=risk_score,
            weight=self.model_weights["debt_risk"],
            risk_level=risk_level,
            description=description
        )
    
    def assess_credit_history_risk(self, metrics: Dict[str, float]) -> RiskFactor:
        """
        评估信用历史风险

        Args:
            metrics: 个人信贷指标字典

        Returns:
            信用历史风险因子
        """
        # 获取信用历史相关指标
        credit_score = metrics.get("信用评分", 600)
        payment_history = metrics.get("还款历史", 0.8)  # 假设0-1之间，1为完美还款

        # 计算信用历史风险评分
        risk_score = 0.0

        # 信用评分评估
        if credit_score < 500:
            risk_score += 0.5
        elif credit_score < 600:
            risk_score += 0.3
        elif credit_score < 700:
            risk_score += 0.15

        # 还款历史评估
        if payment_history < 0.6:
            risk_score += 0.3
        elif payment_history < 0.8:
            risk_score += 0.2
        elif payment_history < 0.9:
            risk_score += 0.1

        # 确定风险等级
        if risk_score >= 0.6:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.4:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        description = f"信用评分: {credit_score}, 还款历史评分: {payment_history:.1f}"

        return RiskFactor(
            name="信用历史风险",
            value=risk_score,
            weight=self.model_weights["credit_history_risk"],
            risk_level=risk_level,
            description=description
        )
    
    def assess_stability_risk(self, metrics: Dict[str, float]) -> RiskFactor:
        """
        评估稳定性风险

        Args:
            metrics: 个人信贷指标字典

        Returns:
            稳定性风险因子
        """
        # 获取稳定性相关指标
        employment_years = metrics.get("工作年限", 2)
        residence_years = metrics.get("居住年限", 2)
        marital_status = metrics.get("婚姻状况", "未知")

        # 计算稳定性风险评分
        risk_score = 0.0

        # 工作年限评估
        if employment_years < 1:
            risk_score += 0.3
        elif employment_years < 2:
            risk_score += 0.2
        elif employment_years < 3:
            risk_score += 0.1

        # 居住年限评估
        if residence_years < 1:
            risk_score += 0.2
        elif residence_years < 2:
            risk_score += 0.1

        # 婚姻状况评估（已婚通常更稳定）
        if isinstance(marital_status, str):
            if "未婚" in marital_status or "单身" in marital_status:
                risk_score += 0.1

        # 确定风险等级
        if risk_score >= 0.4:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        description = f"工作年限: {employment_years}年, 居住年限: {residence_years}年, 婚姻: {marital_status}"

        return RiskFactor(
            name="稳定性风险",
            value=risk_score,
            weight=self.model_weights["stability_risk"],
            risk_level=risk_level,
            description=description
        )
    
    def assess_demographic_risk(self, metrics: Dict[str, float]) -> RiskFactor:
        """
        评估人口统计学风险

        Args:
            metrics: 个人信贷指标字典

        Returns:
            人口统计学风险因子
        """
        # 获取人口统计学相关指标
        age = metrics.get("年龄", 30)
        education_level = metrics.get("教育水平", "本科")
        family_size = metrics.get("家庭人口", 3)

        # 计算人口统计学风险评分
        risk_score = 0.0

        # 年龄评估
        if age < 22 or age > 60:
            risk_score += 0.2
        elif age < 25 or age > 50:
            risk_score += 0.1

        # 教育水平评估
        if isinstance(education_level, str):
            education_lower = education_level.lower()
            if "初中" in education_lower or "小学" in education_lower:
                risk_score += 0.15
            elif "高中" in education_lower or "中专" in education_lower:
                risk_score += 0.1

        # 家庭负担评估
        if family_size > 5:
            risk_score += 0.1

        # 确定风险等级
        if risk_score >= 0.3:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.15:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW

        description = f"年龄: {age}岁, 教育: {education_level}, 家庭人口: {family_size}人"

        return RiskFactor(
            name="人口统计学风险",
            value=risk_score,
            weight=self.model_weights["demographic_risk"],
            risk_level=risk_level,
            description=description
        )
    
    def generate_warning_signals(self, risk_factors: List[RiskFactor], metrics: Dict[str, float]) -> List[str]:
        """
        生成风险预警信号
        
        Args:
            risk_factors: 风险因子列表
            metrics: 财务指标
            
        Returns:
            预警信号列表
        """
        warnings = []
        
        # 检查高风险因子
        for factor in risk_factors:
            if factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                warnings.append(f"⚠️ {factor.name}处于{factor.risk_level.value}状态: {factor.description}")
        
        # 个人信贷特定指标预警
        debt_to_income = metrics.get("负债收入比", 0.3)
        if debt_to_income > 0.5:
            warnings.append(f"⚠️ 负债收入比过高({debt_to_income:.2%})，还款压力较大")

        monthly_income = metrics.get("月收入", 5000)
        if monthly_income < 3000:
            warnings.append(f"⚠️ 月收入较低({monthly_income:,.0f}元)，还款能力不足")

        credit_score = metrics.get("信用评分", 600)
        if credit_score < 600:
            warnings.append(f"⚠️ 信用评分较低({credit_score})，信用风险较高")

        employment_years = metrics.get("工作年限", 2)
        if employment_years < 1:
            warnings.append(f"⚠️ 工作年限过短({employment_years}年)，收入稳定性存疑")
        
        return warnings
    
    def generate_recommendations(self, risk_factors: List[RiskFactor], metrics: Dict[str, float]) -> List[str]:
        """
        生成风险管控建议
        
        Args:
            risk_factors: 风险因子列表
            metrics: 财务指标
            
        Returns:
            建议列表
        """
        recommendations = []
        
        # 基于个人信贷风险因子的建议
        for factor in risk_factors:
            if factor.name == "收入风险" and factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                recommendations.append("💡 建议提供额外收入证明或担保，降低贷款金额")

            elif factor.name == "负债风险" and factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                recommendations.append("💡 建议先偿还部分现有债务，降低负债收入比")

            elif factor.name == "信用历史风险" and factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                recommendations.append("💡 建议提供担保或抵押，或考虑拒绝放贷")

            elif factor.name == "稳定性风险" and factor.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                recommendations.append("💡 建议等待工作稳定后再申请，或提供额外稳定性证明")
        
        # 个人信贷通用建议
        recommendations.extend([
            "💡 建议定期监控个人信用状况变化",
            "💡 建议建立良好的还款习惯",
            "💡 建议合理规划个人财务和债务结构"
        ])
        
        return recommendations
    
    def comprehensive_risk_assessment(self, metrics: Dict[str, float]) -> RiskAssessmentResult:
        """
        综合风险评估
        
        Args:
            metrics: 财务指标字典
            
        Returns:
            综合风险评估结果
        """
        logger.info("开始综合风险评估")
        
        # 评估各类个人信贷风险
        risk_factors = [
            self.assess_income_risk(metrics),
            self.assess_debt_risk(metrics),
            self.assess_credit_history_risk(metrics),
            self.assess_stability_risk(metrics),
            self.assess_demographic_risk(metrics)
        ]
        
        # 计算综合风险评分
        weighted_score = sum(factor.value * factor.weight for factor in risk_factors)
        
        # 确定综合风险等级
        if weighted_score >= 0.6:
            overall_risk_level = RiskLevel.CRITICAL
        elif weighted_score >= 0.4:
            overall_risk_level = RiskLevel.HIGH
        elif weighted_score >= 0.2:
            overall_risk_level = RiskLevel.MEDIUM
        else:
            overall_risk_level = RiskLevel.LOW
        
        # 计算违约概率（基于风险评分的逻辑回归模型）
        default_probability = min(1 / (1 + np.exp(-5 * (weighted_score - 0.5))), 0.95)
        
        # 计算置信度
        confidence_score = 0.8  # 简化实现，实际应基于数据质量和模型可靠性
        
        # 生成预警信号和建议
        warning_signals = self.generate_warning_signals(risk_factors, metrics)
        recommendations = self.generate_recommendations(risk_factors, metrics)
        
        result = RiskAssessmentResult(
            overall_risk_score=weighted_score,
            risk_level=overall_risk_level,
            default_probability=default_probability,
            risk_factors=risk_factors,
            warning_signals=warning_signals,
            recommendations=recommendations,
            confidence_score=confidence_score
        )
        
        logger.info(f"综合风险评估完成: 风险等级={overall_risk_level.value}, 违约概率={default_probability:.2%}")
        return result
